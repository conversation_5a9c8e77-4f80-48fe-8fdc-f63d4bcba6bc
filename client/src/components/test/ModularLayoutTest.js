import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Switch,
  FormControlLabel,
  Checkbox,
  Button,
  Divider,
  Chip,
  Badge,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Close,
  Settings,
  Group,
  Assignment,
  History,
  LocalOffer,
  Description,
  Chat,
  Edit,
  Add,
  Save,
  Cancel
} from '@mui/icons-material';
// import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

// Icon mapping function
const getIconComponent = (iconName) => {
  const iconMap = {
    Description,
    Chat,
    Group,
    LocalOffer,
    Assignment,
    History,
    Close,
    Settings,
    Edit,
    Add,
    Save,
    Cancel
  };
  return iconMap[iconName] || Description; // fallback to Description if icon not found
};

// Mock data for testing
const mockData = {
  project: {
    name: "E-Commerce Platform",
    description: "<p>A comprehensive e-commerce platform with modern features including user authentication, product catalog, shopping cart, payment processing, and order management.</p>",
    members: [
      { user: { _id: '1', username: 'john_doe', firstName: 'John', lastName: 'Doe', color: '#1976d2' } },
      { user: { _id: '2', username: 'jane_smith', firstName: 'Jane', lastName: 'Smith', color: '#d32f2f' } },
      { user: { _id: '3', username: 'bob_wilson', firstName: 'Bob', lastName: 'Wilson', color: '#388e3c' } }
    ]
  },
  comments: [
    { _id: '1', text: 'This looks great! I think we should also consider mobile responsiveness.', user: { username: 'john_doe', color: '#1976d2' }, createdAt: new Date(), version: 1 },
    { _id: '2', text: 'Agreed on mobile. Also, what about accessibility features?', user: { username: 'jane_smith', color: '#d32f2f' }, createdAt: new Date(), version: 1 },
    { _id: '3', text: 'Good point on accessibility. Let\'s add WCAG compliance to the requirements.', user: { username: 'bob_wilson', color: '#388e3c' }, createdAt: new Date(), version: 2 }
  ],
  tests: [
    { id: '1', name: 'User Authentication', status: 'pass', lastRun: new Date() },
    { id: '2', name: 'Product Catalog', status: 'fail', lastRun: new Date() },
    { id: '3', name: 'Shopping Cart', status: 'pending', lastRun: null }
  ],
  documents: [
    { id: '1', name: 'Requirements Document v1.0', type: 'SRD', createdAt: new Date() },
    { id: '2', name: 'Release Notes v2.1', type: 'Release Notes', createdAt: new Date() }
  ],
  tags: [
    { id: '1', name: 'v1.0', color: '#1976d2' },
    { id: '2', name: 'MVP', color: '#388e3c' },
    { id: '3', name: 'Beta', color: '#f57c00' }
  ]
};

// Default module configuration
const defaultModules = [
  { id: 'description', name: 'Description', icon: 'Description', enabled: true, position: { w: 6, h: 4, order: 0 } },
  { id: 'comments', name: 'Comments', icon: 'Chat', enabled: true, position: { w: 6, h: 4, order: 1 } },
  { id: 'userManagement', name: 'User Management', icon: 'Group', enabled: false, position: { w: 4, h: 3, order: 2 } },
  { id: 'releaseManagement', name: 'Release Management', icon: 'LocalOffer', enabled: false, position: { w: 4, h: 3, order: 3 } },
  { id: 'testManagement', name: 'Test Management', icon: 'Assignment', enabled: false, position: { w: 4, h: 3, order: 4 } },
  { id: 'documentHistory', name: 'Document History', icon: 'History', enabled: false, position: { w: 12, h: 2, order: 5 } }
];

const ModularLayoutTest = () => {
  const [modules, setModules] = useState(defaultModules);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [preserveAcrossElements, setPreserveAcrossElements] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [comment, setComment] = useState('');

  // Load saved preferences
  useEffect(() => {
    const savedModules = localStorage.getItem('modularLayout_modules');
    const savedPreserve = localStorage.getItem('modularLayout_preserveAcrossElements');
    
    if (savedModules) {
      setModules(JSON.parse(savedModules));
    }
    if (savedPreserve) {
      setPreserveAcrossElements(JSON.parse(savedPreserve));
    }
  }, []);

  // Save preferences
  const savePreferences = useCallback(() => {
    localStorage.setItem('modularLayout_modules', JSON.stringify(modules));
    localStorage.setItem('modularLayout_preserveAcrossElements', JSON.stringify(preserveAcrossElements));
  }, [modules, preserveAcrossElements]);

  // Auto-save when modules change
  useEffect(() => {
    savePreferences();
  }, [modules, preserveAcrossElements, savePreferences]);

  // Toggle module visibility
  const toggleModule = (moduleId) => {
    setModules(prev => prev.map(module => 
      module.id === moduleId 
        ? { ...module, enabled: !module.enabled }
        : module
    ));
  };

  // Reset to default layout
  const resetToDefault = () => {
    setModules(defaultModules);
    setPreserveAcrossElements(false);
  };



  // Calculate responsive grid columns
  const getGridColumns = (width) => {
    if (width <= 4) return width;
    if (width <= 6) return Math.min(6, width);
    if (width <= 8) return Math.min(8, width);
    return Math.min(12, width);
  };

  // Handle module reordering
  const moveModule = (moduleId, direction) => {
    const enabledModules = modules.filter(m => m.enabled);
    const currentIndex = enabledModules.findIndex(m => m.id === moduleId);

    if (direction === 'up' && currentIndex > 0) {
      const newOrder = [...enabledModules];
      [newOrder[currentIndex], newOrder[currentIndex - 1]] = [newOrder[currentIndex - 1], newOrder[currentIndex]];

      const updatedModules = modules.map(module => {
        if (!module.enabled) return module;
        const newIndex = newOrder.findIndex(item => item.id === module.id);
        return { ...module, position: { ...module.position, order: newIndex } };
      });

      setModules(updatedModules);
    } else if (direction === 'down' && currentIndex < enabledModules.length - 1) {
      const newOrder = [...enabledModules];
      [newOrder[currentIndex], newOrder[currentIndex + 1]] = [newOrder[currentIndex + 1], newOrder[currentIndex]];

      const updatedModules = modules.map(module => {
        if (!module.enabled) return module;
        const newIndex = newOrder.findIndex(item => item.id === module.id);
        return { ...module, position: { ...module.position, order: newIndex } };
      });

      setModules(updatedModules);
    }
  };

  // Render module content
  const renderModuleContent = (module) => {
    switch (module.id) {
      case 'description':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Description</Typography>
              {editMode && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button size="small" startIcon={<Save />}>Save</Button>
                  <Button size="small" startIcon={<Cancel />}>Cancel</Button>
                </Box>
              )}
            </Box>
            <Box 
              sx={{ 
                minHeight: 200,
                border: editMode ? '2px dashed #1976d2' : 'none',
                borderRadius: 1,
                p: editMode ? 2 : 0
              }}
              dangerouslySetInnerHTML={{ __html: mockData.project.description }}
            />
          </Box>
        );

      case 'comments':
        return (
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                <Chat sx={{ mr: 1, color: '#1976d2' }} />
                Comments
              </Typography>
              <Badge badgeContent={mockData.comments.length} color="primary" />
            </Box>
            <List sx={{ maxHeight: 300, overflow: 'auto' }}>
              {mockData.comments.map((comment) => (
                <ListItem key={comment._id} sx={{ borderRadius: 1, mb: 1 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: comment.user.color, width: 32, height: 32 }}>
                      {comment.user.username[0].toUpperCase()}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2">{comment.user.username}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          (v{comment.version})
                        </Typography>
                      </Box>
                    }
                    secondary={comment.text}
                  />
                </ListItem>
              ))}
            </List>
            <Box sx={{ mt: 2 }}>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="Add a comment..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                variant="outlined"
              />
              <Button
                variant="contained"
                startIcon={<Add />}
                disabled={!comment.trim()}
                sx={{ mt: 1, float: 'right' }}
              >
                Add Comment
              </Button>
            </Box>
          </Box>
        );

      case 'userManagement':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">User Management</Typography>
              <Button size="small" startIcon={<Add />}>Add User</Button>
            </Box>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {mockData.project.members.map((member) => (
                <Chip
                  key={member.user._id}
                  label={member.user.username}
                  avatar={
                    <Avatar sx={{ bgcolor: member.user.color, width: 24, height: 24 }}>
                      {member.user.username[0].toUpperCase()}
                    </Avatar>
                  }
                  sx={{ bgcolor: member.user.color + '20' }}
                />
              ))}
            </Box>
          </Box>
        );

      case 'testManagement':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Test Management</Typography>
              <Badge badgeContent={mockData.tests.filter(t => t.status === 'fail').length} color="error" />
            </Box>
            <List dense>
              {mockData.tests.map((test) => (
                <ListItem key={test.id}>
                  <ListItemText
                    primary={test.name}
                    secondary={
                      <Chip 
                        label={test.status} 
                        size="small" 
                        color={test.status === 'pass' ? 'success' : test.status === 'fail' ? 'error' : 'default'}
                      />
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        );

      case 'releaseManagement':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Release Management</Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {mockData.tags.map((tag) => (
                <Chip
                  key={tag.id}
                  label={tag.name}
                  sx={{ bgcolor: tag.color + '20', color: tag.color }}
                />
              ))}
            </Box>
          </Box>
        );

      case 'documentHistory':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Document History</Typography>
            <List dense>
              {mockData.documents.map((doc) => (
                <ListItem key={doc.id}>
                  <ListItemText
                    primary={doc.name}
                    secondary={`${doc.type} • ${doc.createdAt.toLocaleDateString()}`}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        );

      default:
        return <Typography>Module content</Typography>;
    }
  };

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4">Modular Layout Test (Layout 4)</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Use ↑↓ buttons to reorder • Resize with corner handles • Configure visibility in settings
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Chip
            label={`${modules.filter(m => m.enabled).length} modules active`}
            color="primary"
            variant="outlined"
          />
          <Button
            variant="outlined"
            startIcon={editMode ? <Save /> : <Edit />}
            onClick={() => setEditMode(!editMode)}
          >
            {editMode ? 'Save' : 'Edit'}
          </Button>
          <IconButton onClick={() => setSettingsOpen(true)}>
            <Settings />
          </IconButton>
        </Box>
      </Box>

      {/* Module Grid */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(12, 1fr)',
          gap: 3,
          minHeight: '600px'
        }}
      >
        {modules
          .filter(module => module.enabled)
          .sort((a, b) => (a.position.order || 0) - (b.position.order || 0))
          .map((module, index) => {
            const IconComponent = getIconComponent(module.icon);
            const enabledModules = modules.filter(m => m.enabled);
            const isFirst = index === 0;
            const isLast = index === enabledModules.length - 1;

            return (
              <Paper
                key={module.id}
                sx={{
                  gridColumn: `span ${getGridColumns(module.position.w)}`,
                  minHeight: `${module.position.h * 100}px`,
                  p: 3,
                  borderRadius: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                  transition: 'all 0.2s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  display: 'flex',
                  flexDirection: 'column',
                  '&:hover': {
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                {/* Module Header */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <IconComponent color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, flex: 1 }}>
                    {module.name}
                  </Typography>

                  {/* Reorder Buttons */}
                  <Box sx={{ display: 'flex', gap: 0.5, mr: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => moveModule(module.id, 'up')}
                      disabled={isFirst}
                      sx={{ opacity: isFirst ? 0.3 : 0.7 }}
                    >
                      <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>↑</Typography>
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => moveModule(module.id, 'down')}
                      disabled={isLast}
                      sx={{ opacity: isLast ? 0.3 : 0.7 }}
                    >
                      <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>↓</Typography>
                    </IconButton>
                  </Box>

                  <IconButton
                    size="small"
                    onClick={() => toggleModule(module.id)}
                    sx={{ opacity: 0.7 }}
                  >
                    <Close fontSize="small" />
                  </IconButton>
                </Box>

                          {/* Module Content */}
                          <Box sx={{ flex: 1, overflow: 'auto' }}>
                            {renderModuleContent(module)}
                          </Box>

                          {/* Resize Handles */}
                          <Box
                            sx={{
                              position: 'absolute',
                              bottom: 0,
                              right: 0,
                              width: 20,
                              height: 20,
                              cursor: 'nw-resize',
                              background: 'linear-gradient(-45deg, transparent 30%, #1976d2 30%, #1976d2 70%, transparent 70%)',
                              opacity: 0.3,
                              '&:hover': { opacity: 0.7 }
                            }}
                            onMouseDown={(e) => {
                              e.preventDefault();
                              // TODO: Implement resize functionality
                            }}
                  />
              </Paper>
            );
          })}
      </Box>

      {/* Settings Dialog */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Settings />
            Layout Settings
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Customize your workspace by enabling/disabling modules. Changes are automatically saved.
          </Typography>

          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>Module Visibility</Typography>
          {modules.map((module) => {
            const IconComponent = getIconComponent(module.icon);
            return (
              <FormControlLabel
                key={module.id}
                control={
                  <Switch
                    checked={module.enabled}
                    onChange={() => toggleModule(module.id)}
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <IconComponent fontSize="small" />
                    {module.name}
                  </Box>
                }
                sx={{ display: 'block', mb: 1 }}
              />
            );
          })}
          
          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>Persistence Options</Typography>
          <FormControlLabel
            control={
              <Checkbox
                checked={preserveAcrossElements}
                onChange={(e) => setPreserveAcrossElements(e.target.checked)}
              />
            }
            label="Preserve layout across elements (Projects, Features, Requirements)"
          />
          <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mb: 2 }}>
            When enabled, your layout will remain the same when navigating between different projects, features, and requirements.
            When disabled, each new element will revert to the default layout.
          </Typography>

          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>How to Use</Typography>
          <Box component="ul" sx={{ pl: 2, m: 0 }}>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              <strong>Reorder:</strong> Use the ↑ and ↓ buttons in each module header to change order
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              <strong>Resize:</strong> Drag the resize handle in the bottom-right corner of each module
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              <strong>Hide/Show:</strong> Use the switches above or click the × button on each module
            </Typography>
            <Typography component="li" variant="body2">
              <strong>Reset:</strong> Use the "Reset to Default" button to restore the original layout
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={resetToDefault} color="error">
            Reset to Default
          </Button>
          <Button onClick={() => setSettingsOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ModularLayoutTest;
