import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  FormControlLabel,
  Checkbox,
  Button,
  Divider,
  Chip,
  Badge,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Close,
  Settings,
  Group,
  Assignment,
  History,
  LocalOffer,
  Description,
  Chat,
  Edit,
  Add,
  Save,
  Cancel
} from '@mui/icons-material';
import { Responsive, WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

// Create responsive grid layout
const ResponsiveGridLayout = WidthProvider(Responsive);

// Icon mapping function
const getIconComponent = (iconName) => {
  const iconMap = {
    Description,
    Chat,
    Group,
    LocalOffer,
    Assignment,
    History,
    Close,
    Settings,
    Edit,
    Add,
    Save,
    Cancel
  };
  return iconMap[iconName] || Description;
};

// Mock data for testing
const mockData = {
  project: {
    name: "E-Commerce Platform",
    description: "<p>A comprehensive e-commerce platform with modern features including user authentication, product catalog, shopping cart, payment processing, and order management.</p>",
    members: [
      { user: { _id: '1', username: 'john_doe', firstName: 'John', lastName: 'Doe', color: '#1976d2' } },
      { user: { _id: '2', username: 'jane_smith', firstName: 'Jane', lastName: 'Smith', color: '#d32f2f' } },
      { user: { _id: '3', username: 'bob_wilson', firstName: 'Bob', lastName: 'Wilson', color: '#388e3c' } }
    ]
  },
  comments: [
    { _id: '1', text: 'This looks great! I think we should also consider mobile responsiveness.', user: { username: 'john_doe', color: '#1976d2' }, createdAt: new Date(), version: 1 },
    { _id: '2', text: 'Agreed on mobile. Also, what about accessibility features?', user: { username: 'jane_smith', color: '#d32f2f' }, createdAt: new Date(), version: 1 },
    { _id: '3', text: 'Good point on accessibility. Let\'s add WCAG compliance to the requirements.', user: { username: 'bob_wilson', color: '#388e3c' }, createdAt: new Date(), version: 2 }
  ],
  tests: [
    { id: '1', name: 'User Authentication', status: 'pass', lastRun: new Date() },
    { id: '2', name: 'Product Catalog', status: 'fail', lastRun: new Date() },
    { id: '3', name: 'Shopping Cart', status: 'pending', lastRun: null }
  ],
  documents: [
    { id: '1', name: 'Requirements Document v1.0', type: 'SRD', createdAt: new Date() },
    { id: '2', name: 'Release Notes v2.1', type: 'Release Notes', createdAt: new Date() }
  ],
  tags: [
    { id: '1', name: 'v1.0', color: '#1976d2' },
    { id: '2', name: 'MVP', color: '#388e3c' },
    { id: '3', name: 'Beta', color: '#f57c00' }
  ]
};

// Default module configuration for react-grid-layout
const defaultModules = [
  { id: 'description', name: 'Description', icon: 'Description', enabled: true },
  { id: 'comments', name: 'Comments', icon: 'Chat', enabled: true },
  { id: 'userManagement', name: 'User Management', icon: 'Group', enabled: false },
  { id: 'releaseManagement', name: 'Release Management', icon: 'LocalOffer', enabled: false },
  { id: 'testManagement', name: 'Test Management', icon: 'Assignment', enabled: false },
  { id: 'documentHistory', name: 'Document History', icon: 'History', enabled: false }
];

// Default layout configuration for react-grid-layout
const defaultLayouts = {
  lg: [
    { i: 'description', x: 0, y: 0, w: 6, h: 6 },
    { i: 'comments', x: 6, y: 0, w: 6, h: 6 },
    { i: 'userManagement', x: 0, y: 6, w: 6, h: 5 },
    { i: 'releaseManagement', x: 6, y: 6, w: 6, h: 5 },
    { i: 'testManagement', x: 0, y: 11, w: 6, h: 5 },
    { i: 'documentHistory', x: 6, y: 11, w: 6, h: 4 }
  ],
  md: [
    { i: 'description', x: 0, y: 0, w: 5, h: 6 },
    { i: 'comments', x: 5, y: 0, w: 5, h: 6 },
    { i: 'userManagement', x: 0, y: 6, w: 5, h: 5 },
    { i: 'releaseManagement', x: 5, y: 6, w: 5, h: 5 },
    { i: 'testManagement', x: 0, y: 11, w: 5, h: 5 },
    { i: 'documentHistory', x: 5, y: 11, w: 5, h: 4 }
  ],
  sm: [
    { i: 'description', x: 0, y: 0, w: 3, h: 6 },
    { i: 'comments', x: 3, y: 0, w: 3, h: 6 },
    { i: 'userManagement', x: 0, y: 6, w: 3, h: 5 },
    { i: 'releaseManagement', x: 3, y: 6, w: 3, h: 5 },
    { i: 'testManagement', x: 0, y: 11, w: 3, h: 5 },
    { i: 'documentHistory', x: 3, y: 11, w: 3, h: 4 }
  ],
  xs: [
    { i: 'description', x: 0, y: 0, w: 4, h: 6 },
    { i: 'comments', x: 0, y: 6, w: 4, h: 6 },
    { i: 'userManagement', x: 0, y: 12, w: 4, h: 5 },
    { i: 'releaseManagement', x: 0, y: 17, w: 4, h: 5 },
    { i: 'testManagement', x: 0, y: 22, w: 4, h: 5 },
    { i: 'documentHistory', x: 0, y: 27, w: 4, h: 4 }
  ],
  xxs: [
    { i: 'description', x: 0, y: 0, w: 2, h: 6 },
    { i: 'comments', x: 0, y: 6, w: 2, h: 6 },
    { i: 'userManagement', x: 0, y: 12, w: 2, h: 5 },
    { i: 'releaseManagement', x: 0, y: 17, w: 2, h: 5 },
    { i: 'testManagement', x: 0, y: 22, w: 2, h: 5 },
    { i: 'documentHistory', x: 0, y: 27, w: 2, h: 4 }
  ]
};

// Default sizes for new modules (when toggled on) - responsive
const getDefaultModuleSize = (moduleId, breakpoint = 'lg') => {
  const defaults = {
    lg: {
      description: { w: 6, h: 6 },
      comments: { w: 6, h: 6 },
      userManagement: { w: 6, h: 5 },
      releaseManagement: { w: 6, h: 5 },
      testManagement: { w: 6, h: 5 },
      documentHistory: { w: 6, h: 4 }
    },
    md: {
      description: { w: 5, h: 6 },
      comments: { w: 5, h: 6 },
      userManagement: { w: 5, h: 5 },
      releaseManagement: { w: 5, h: 5 },
      testManagement: { w: 5, h: 5 },
      documentHistory: { w: 5, h: 4 }
    },
    sm: {
      description: { w: 3, h: 6 },
      comments: { w: 3, h: 6 },
      userManagement: { w: 3, h: 5 },
      releaseManagement: { w: 3, h: 5 },
      testManagement: { w: 3, h: 5 },
      documentHistory: { w: 3, h: 4 }
    },
    xs: {
      description: { w: 4, h: 6 },
      comments: { w: 4, h: 6 },
      userManagement: { w: 4, h: 5 },
      releaseManagement: { w: 4, h: 5 },
      testManagement: { w: 4, h: 5 },
      documentHistory: { w: 4, h: 4 }
    },
    xxs: {
      description: { w: 2, h: 6 },
      comments: { w: 2, h: 6 },
      userManagement: { w: 2, h: 5 },
      releaseManagement: { w: 2, h: 5 },
      testManagement: { w: 2, h: 5 },
      documentHistory: { w: 2, h: 4 }
    }
  };

  const breakpointDefaults = defaults[breakpoint] || defaults.lg;
  return breakpointDefaults[moduleId] || { w: breakpointDefaults.description.w, h: 5 };
};

const ModularLayoutTest = () => {
  const [modules, setModules] = useState(defaultModules);
  const [layouts, setLayouts] = useState(defaultLayouts);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [preserveAcrossElements, setPreserveAcrossElements] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [comment, setComment] = useState('');

  // Load saved preferences
  useEffect(() => {
    const savedModules = localStorage.getItem('modularLayout_modules');
    const savedLayouts = localStorage.getItem('modularLayout_layouts');
    const savedPreserve = localStorage.getItem('modularLayout_preserveAcrossElements');

    if (savedModules) {
      setModules(JSON.parse(savedModules));
    }
    if (savedLayouts) {
      setLayouts(JSON.parse(savedLayouts));
    }
    if (savedPreserve) {
      setPreserveAcrossElements(JSON.parse(savedPreserve));
    }
  }, []);

  // Save preferences
  const savePreferences = useCallback(() => {
    localStorage.setItem('modularLayout_modules', JSON.stringify(modules));
    localStorage.setItem('modularLayout_layouts', JSON.stringify(layouts));
    localStorage.setItem('modularLayout_preserveAcrossElements', JSON.stringify(preserveAcrossElements));
  }, [modules, layouts, preserveAcrossElements]);

  // Auto-save when modules or layouts change
  useEffect(() => {
    savePreferences();
  }, [modules, layouts, preserveAcrossElements, savePreferences]);

  // Toggle module visibility
  const toggleModule = (moduleId) => {
    setModules(prev => {
      const updatedModules = prev.map(module =>
        module.id === moduleId
          ? { ...module, enabled: !module.enabled }
          : module
      );

      // Update layouts when toggling modules
      const toggledModule = updatedModules.find(m => m.id === moduleId);

      setLayouts(prevLayouts => {
        const newLayouts = { ...prevLayouts };

        Object.keys(newLayouts).forEach(breakpoint => {
          if (toggledModule.enabled) {
            // Module is being enabled - add it to layout if not present
            const existingItem = newLayouts[breakpoint].find(item => item.i === moduleId);
            if (!existingItem) {
              // Find a good position for the new module
              const maxY = newLayouts[breakpoint].reduce((max, item) =>
                Math.max(max, item.y + item.h), 0
              );

              const defaultSize = getDefaultModuleSize(moduleId, breakpoint);
              newLayouts[breakpoint].push({
                i: moduleId,
                x: 0,
                y: maxY,
                w: defaultSize.w,
                h: defaultSize.h
              });
            }
          } else {
            // Module is being disabled - remove it from layout
            newLayouts[breakpoint] = newLayouts[breakpoint].filter(item => item.i !== moduleId);
          }
        });

        return newLayouts;
      });

      return updatedModules;
    });
  };

  // Reset to default layout
  const resetToDefault = () => {
    setModules(defaultModules);
    setLayouts(defaultLayouts);
    setPreserveAcrossElements(false);
  };

  // Handle layout changes (drag/resize)
  const onLayoutChange = (layout, layouts) => {
    setLayouts(layouts);
  };

  // Get enabled modules for rendering
  const getEnabledModules = () => {
    return modules.filter(module => module.enabled);
  };

  // Render module content
  const renderModuleContent = (module) => {
    switch (module.id) {
      case 'description':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Description</Typography>
              {editMode && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button size="small" startIcon={<Save />}>Save</Button>
                  <Button size="small" startIcon={<Cancel />}>Cancel</Button>
                </Box>
              )}
            </Box>
            <Box 
              sx={{ 
                minHeight: 200,
                border: editMode ? '2px dashed #1976d2' : 'none',
                borderRadius: 1,
                p: editMode ? 2 : 0
              }}
              dangerouslySetInnerHTML={{ __html: mockData.project.description }}
            />
          </Box>
        );

      case 'comments':
        return (
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                <Chat sx={{ mr: 1, color: '#1976d2' }} />
                Comments
              </Typography>
              <Badge badgeContent={mockData.comments.length} color="primary" />
            </Box>
            <List sx={{ maxHeight: 300, overflow: 'auto' }}>
              {mockData.comments.map((comment) => (
                <ListItem key={comment._id} sx={{ borderRadius: 1, mb: 1 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: comment.user.color, width: 32, height: 32 }}>
                      {comment.user.username[0].toUpperCase()}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2">{comment.user.username}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          (v{comment.version})
                        </Typography>
                      </Box>
                    }
                    secondary={comment.text}
                  />
                </ListItem>
              ))}
            </List>
            <Box sx={{ mt: 2 }}>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="Add a comment..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                variant="outlined"
              />
              <Button
                variant="contained"
                startIcon={<Add />}
                disabled={!comment.trim()}
                sx={{ mt: 1, float: 'right' }}
              >
                Add Comment
              </Button>
            </Box>
          </Box>
        );

      case 'userManagement':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">User Management</Typography>
              <Button size="small" startIcon={<Add />}>Add User</Button>
            </Box>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {mockData.project.members.map((member) => (
                <Chip
                  key={member.user._id}
                  label={member.user.username}
                  avatar={
                    <Avatar sx={{ bgcolor: member.user.color, width: 24, height: 24 }}>
                      {member.user.username[0].toUpperCase()}
                    </Avatar>
                  }
                  sx={{ bgcolor: member.user.color + '20' }}
                />
              ))}
            </Box>
          </Box>
        );

      case 'testManagement':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Test Management</Typography>
              <Badge badgeContent={mockData.tests.filter(t => t.status === 'fail').length} color="error" />
            </Box>
            <List dense>
              {mockData.tests.map((test) => (
                <ListItem key={test.id}>
                  <ListItemText
                    primary={test.name}
                    secondary={
                      <Chip 
                        label={test.status} 
                        size="small" 
                        color={test.status === 'pass' ? 'success' : test.status === 'fail' ? 'error' : 'default'}
                      />
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        );

      case 'releaseManagement':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Release Management</Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {mockData.tags.map((tag) => (
                <Chip
                  key={tag.id}
                  label={tag.name}
                  sx={{ bgcolor: tag.color + '20', color: tag.color }}
                />
              ))}
            </Box>
          </Box>
        );

      case 'documentHistory':
        return (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>Document History</Typography>
            <List dense>
              {mockData.documents.map((doc) => (
                <ListItem key={doc.id}>
                  <ListItemText
                    primary={doc.name}
                    secondary={`${doc.type} • ${doc.createdAt.toLocaleDateString()}`}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        );

      default:
        return <Typography>Module content</Typography>;
    }
  };

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4">Modular Layout Test (React Grid Layout)</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Drag modules to reorder • Drag edges to resize • Configure visibility in settings
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Chip
            label={`${getEnabledModules().length} modules active`}
            color="primary"
            variant="outlined"
          />
          <Button
            variant="outlined"
            startIcon={editMode ? <Save /> : <Edit />}
            onClick={() => setEditMode(!editMode)}
          >
            {editMode ? 'Save' : 'Edit'}
          </Button>
          <IconButton onClick={() => setSettingsOpen(true)}>
            <Settings />
          </IconButton>
        </Box>
      </Box>

      {/* React Grid Layout */}
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        onLayoutChange={onLayoutChange}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={60}
        isDraggable={true}
        isResizable={true}
        margin={[16, 16]}
        containerPadding={[0, 0]}
        useCSSTransforms={true}
      >
        {getEnabledModules().map((module) => {
          const IconComponent = getIconComponent(module.icon);

          return (
            <Paper
              key={module.id}
              sx={{
                p: 2,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                transition: 'all 0.2s ease',
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                },
                // Custom styles for drag handle
                '& .react-grid-dragHandleExample': {
                  cursor: 'move',
                  padding: '4px',
                }
              }}
            >
              {/* Module Header with Drag Handle */}
              <Box
                className="react-grid-dragHandleExample"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 1,
                  cursor: 'move',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)'
                  }
                }}
              >
                <IconComponent color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
                <Typography variant="subtitle2" sx={{ fontWeight: 600, flex: 1 }}>
                  {module.name}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => toggleModule(module.id)}
                  sx={{ opacity: 0.7, ml: 1 }}
                >
                  <Close fontSize="small" />
                </IconButton>
              </Box>

              {/* Module Content */}
              <Box sx={{ flex: 1, overflow: 'auto', minHeight: 0 }}>
                {renderModuleContent(module)}
              </Box>
            </Paper>
          );
        })}
      </ResponsiveGridLayout>

      {/* Settings Dialog */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Settings />
            Layout Settings
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Customize your workspace by enabling/disabling modules. Changes are automatically saved.
          </Typography>

          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>Module Visibility</Typography>
          {modules.map((module) => {
            const IconComponent = getIconComponent(module.icon);
            return (
              <FormControlLabel
                key={module.id}
                control={
                  <Checkbox
                    checked={module.enabled}
                    onChange={() => toggleModule(module.id)}
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <IconComponent fontSize="small" />
                    {module.name}
                  </Box>
                }
                sx={{ display: 'block', mb: 1 }}
              />
            );
          })}
          
          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>Persistence Options</Typography>
          <FormControlLabel
            control={
              <Checkbox
                checked={preserveAcrossElements}
                onChange={(e) => setPreserveAcrossElements(e.target.checked)}
              />
            }
            label="Preserve layout across elements (Projects, Features, Requirements)"
          />
          <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mb: 2 }}>
            When enabled, your layout will remain the same when navigating between different projects, features, and requirements.
            When disabled, each new element will revert to the default layout.
          </Typography>

          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>How to Use</Typography>
          <Box component="ul" sx={{ pl: 2, m: 0 }}>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              <strong>Drag to Reorder:</strong> Drag any module by its header to move it to a new position
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              <strong>Resize:</strong> Drag any edge or corner of a module to resize it
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              <strong>Hide/Show:</strong> Use the checkboxes above or click the × button on each module
            </Typography>
            <Typography component="li" variant="body2">
              <strong>Reset:</strong> Use the "Reset to Default" button to restore the original layout
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={resetToDefault} color="error">
            Reset to Default
          </Button>
          <Button onClick={() => setSettingsOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ModularLayoutTest;
